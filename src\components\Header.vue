<template>
  <el-header v-if="showHeaderAndSidebar && !loading" class="header">
    <div class="header-content">
      <router-link to="/" class="logo">Fuzzing Platform</router-link>
      <el-tooltip content="登出" placement="bottom">
        <el-button type="text" @click="logout" class="logout-btn">
          <el-icon><SwitchButton /></el-icon>
        </el-button>
      </el-tooltip>
    </div>
  </el-header>
</template>

<script>
import { SwitchButton } from '@element-plus/icons-vue'

export default {
  components: {
    SwitchButton,
  },
  props: {
    showHeaderAndSidebar: Boolean,
    loading: Boolean,
  },
  methods: {
    logout() {
      this.$emit('logout');
    },
  },
};
</script>

<style scoped>
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 0;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  transition: all 0.3s ease;
}

.header:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.header-content {
  height: 64px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.logo {
  font-family: 'melete', sans-serif;
  font-size: 26px;
  font-weight: 700;
  text-decoration: none;
  margin-right: auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.logo:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: translateY(-1px);
}

.logout-btn {
  color: #6b7280;
  font-size: 20px;
  transition: all 0.3s ease;
  margin-right: 8px;
  padding: 8px;
  border-radius: 8px;
  background: transparent;
  border: none;
}

.logout-btn:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.logout-btn:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .logo {
    font-size: 22px;
  }

  .logout-btn {
    font-size: 18px;
  }
}
</style>
