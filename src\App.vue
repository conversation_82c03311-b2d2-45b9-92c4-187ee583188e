<template>
  <el-container id="app">
    <Header :showHeaderAndSidebar="showHeaderAndSidebar" :loading="loading" @logout="logout" />
    <el-container class="main-container">
      <Sidebar
        :showHeaderAndSidebar="showHeaderAndSidebar"
        :loading="loading"
        :isCollapsed="isCollapsed"
        :isAdmin="isAdmin"
        @toggle-collapse="toggleCollapse"
      />
      <!-- 主要内容区域 -->
      <el-main v-if="!isHomePage && !isLoginPage && !isRegisterPage" class="main-content">
        <el-skeleton v-if="loading" :rows="5" animated /> <!-- 骨架屏 -->
        <router-view v-else></router-view> <!-- 实际内容 -->
      </el-main>
    </el-container>
    <!-- 首页、登录、注册页面内容 -->
    <router-view v-if="isHomePage || isLoginPage || isRegisterPage" class="full-height"></router-view>
  </el-container>
</template>

<script>
import Header from '@/components/Header.vue';
import Sidebar from '@/components/Sidebar.vue';

export default {
  components: {
    Header,
    Sidebar,
  },
  data() {
    return {
      isCollapsed: false, // 默认展开侧边栏
      loading: true, // 用于控制骨架屏的显示
    };
  },
  computed: {
    isHomePage() {
      return this.$route.path === '/';
    },
    isLoginPage() {
      return this.$route.path === '/login';
    },
    isRegisterPage() {
      return this.$route.path === '/register';
    },
    showHeaderAndSidebar() {
      // 仅在非首页、非登录、非注册页面显示侧边栏和顶部导航
      return !this.isHomePage && !this.isLoginPage && !this.isRegisterPage;
    },
    isAuthenticated() {
      return this.$store.getters.isAuthenticated;
    },
    isAdmin() {
      return this.$store.getters.isAdmin;
    },
  },
  methods: {
    goToLogin() {
      this.$router.push('/login');
    },
    logout() {
      this.$store.dispatch('logout');
      this.$router.push('/');
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
    },
    fetchData() {
      // 模拟数据加载，2秒后关闭骨架屏
      setTimeout(() => {
        this.loading = false;
      }, 1200);
    },
  },
  mounted() {
    this.fetchData(); // 页面加载后获取数据
    // 设置新的主色调
    document.documentElement.style.setProperty('--el-color-primary', '#667eea');
    document.documentElement.style.setProperty('--el-color-primary-light-3', '#8b9df0');
    document.documentElement.style.setProperty('--el-color-primary-light-5', '#a5b4f3');
    document.documentElement.style.setProperty('--el-color-primary-light-7', '#bfcaf6');
    document.documentElement.style.setProperty('--el-color-primary-light-8', '#d2ddf8');
    document.documentElement.style.setProperty('--el-color-primary-light-9', '#e5f0fa');
    document.documentElement.style.setProperty('--el-color-primary-dark-2', '#5a67d8');
  },
};
</script>

<style scoped>
#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #374151;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

.main-content {
  padding: 24px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  color: #374151;
  height: 100%;
  overflow-y: auto;
  border-radius: 16px;
  margin: 16px;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.main-content:hover {
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.07),
    0 1px 3px rgba(0, 0, 0, 0.06);
}

.full-height {
  height: 100%;
  overflow-y: auto;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* 自定义滚动条 */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.5);
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 16px;
    margin: 8px;
    border-radius: 12px;
  }
}
</style>
