<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 text-gray-800 overflow-x-hidden" @mousemove="handleMouseMove">
    <!-- 跟随鼠标的蓝色圆点 -->
    <div class="mouse-follower" :style="mouseFollowerStyle"></div>

    <!-- Hero Section -->
    <section
      class="relative h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100"
    >
      <div class="absolute inset-0 z-0">
        <img
          src="https://ai-public.mastergo.com/ai/img_res/5066ff6207b2fe945fd04e7852b7d616.jpg"
          alt="Background"
          class="w-full h-full object-cover object-center opacity-80"
        />
      </div>
      <div class="relative z-10 text-center px-4">
        <!-- <img src="https://ai-public.mastergo.com/ai/img_res/9db5799e872b53e151928a0dc4c312df.jpg" alt="Logo" class="w-32 h-32 mx-auto mb-6"> -->
        <h1 class="text-7xl font-bold mb-10 bg-gradient-to-r from-cyan-400 via-blue-500 to-teal-500 bg-clip-text text-transparent animate-pulse">
          智能车载系统安全卫士
        </h1>
        <p class="text-2xl mb-8 text-gray-700 font-medium">大模型赋能，精准检测，为您的车载网络保驾护航</p>
        <!-- 在 template 中 -->
        <el-button
          type="primary"
          class="custom-button text-white font-bold py-4 px-16 transition-all duration-300 text-lg relative overflow-hidden"
          @click="goToLogin"
        >
          立即体验
        </el-button>

        <!-- <button @click="goToLogin" class="bg-[#4ED9F5] text-gray-900 px-8 py-3 rounded-full text-lg font-semibold hover:bg-[#3bc8e4] transition-colors duration-300 !rounded-button whitespace-nowrap get-start-button">立即体验</button> -->
      </div>
      <div
        class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce"
      >
        <!-- <i class="fas fa-angle-down text-[#4ED9F5] text-3xl"></i> -->
        <font-awesome-icon
          @click="scrollToCallToAction"
          icon="fa-solid fa-angle-down"
          class="text-[#4ED9F5] text-3xl hover:text-[#3bc8e6] transition-colors duration-300 cursor-pointer animate-bounce"
        />
      </div>
    </section>

    <!-- 图片与下方内容的衔接 -->
    <div class="wave-transition">
      <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="wave-svg">
        <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" class="wave-fill"></path>
        <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" class="wave-fill"></path>
        <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" class="wave-fill"></path>
      </svg>
    </div>

    <!-- Platform Advantages -->
    <section class="py-20 px-4 bg-white relative">
      <!-- 动态背景效果 -->
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
      <h1 class="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-[#4ED9F5] to-[#3bc8e6] bg-clip-text text-transparent">
        平台优势
      </h1>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <div
          v-for="(advantage, index) in advantages"
          :key="index"
          class="flip-card"
          @mouseenter="flipCard(index)"
          @mouseleave="flipCard(index)"
        >
          <div class="flip-card-inner" :class="{ 'flipped': flippedCards[index] }">
            <!-- 正面 -->
            <div class="flip-card-front">
              <i :class="advantage.icon + ' text-[#4ED9F5] text-5xl mb-4'"></i>
              <h3 class="text-xl font-semibold text-gray-800">
                {{ advantage.title }}
              </h3>
            </div>
            <!-- 背面 -->
            <div class="flip-card-back">
              <h3 class="text-xl font-semibold mb-4 text-white">
                {{ advantage.title }}
              </h3>
              <p class="text-white/90 text-sm leading-relaxed">{{ advantage.description }}</p>
              <div class="mt-4">
                <i :class="advantage.icon + ' text-white/70 text-2xl'"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Call to Action -->
    <section ref="callToAction" class="py-20 px-4 text-center bg-gradient-to-b from-white to-blue-50">
      <h1 class="text-4xl font-bold mb-6 text-[#4ED9F5]">
        开始使用智能车载系统安全卫士
      </h1>
      <p class="text-xl mb-8 text-gray-700">
        立即体验大模型赋能的模糊测试，提升您的车载网络安全
      </p>
      <button
        @click="goToLogin"
        class="bg-[#4ED9F5] text-white px-12 py-4 rounded-full text-xl font-semibold hover:bg-[#3EAEC2] transition-all duration-300 transform hover:scale-105 !rounded-button whitespace-nowrap shadow-lg hover:shadow-xl"
      >
        开始使用
      </button>
    </section>

    <!-- Footer -->
    <footer class="bg-white py-8 px-4 border-t border-gray-200">
      <div
        class="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center"
      >
        <div class="text-gray-600 mb-4 md:mb-0">
          © 2025 智能车载系统卫士. 保留所有权利.
        </div>
        <div class="flex space-x-4">
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >隐私政策</a
          >
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >使用条款</a
          >
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >联系我们</a
          >
        </div>
      </div>
    </footer>

     <!-- 光标跟随效果 -->
    <div ref="cursor" class="hidden md:block fixed w-4 h-4 bg-blue-400 rounded-full pointer-events-none opacity-80 z-50"></div>

  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, reactive } from "vue";
import router from "../router";

const advantages = [
  {
    icon: "fas fa-brain",
    title: "智能分析",
    description: "利用大模型技术，智能识别潜在威胁，提高检测准确率",
  },
  {
    icon: "fas fa-shield-alt",
    title: "全面防护",
    description: "覆盖车载网络各个层面，为您的车辆提供全方位安全保障",
  },
  {
    icon: "fas fa-tachometer-alt",
    title: "高效检测",
    description: "快速扫描和分析，及时发现并报告安全隐患",
  },
];

const chartContainer = ref<HTMLElement | null>(null);
const cursor = ref<HTMLElement | null>(null);

// 翻转卡片状态
const flippedCards = reactive([false, false, false]);

// 鼠标跟随效果
const mouseFollowerStyle = ref({
  left: '0px',
  top: '0px',
  transform: 'translate(-50%, -50%)'
});

const handleMouseMove = (event: MouseEvent) => {
  mouseFollowerStyle.value = {
    left: `${event.clientX}px`,
    top: `${event.clientY}px`,
    transform: 'translate(-50%, -50%)'
  };
};

const flipCard = (index: number) => {
  flippedCards[index] = !flippedCards[index];
};

const scrollToCallToAction = () => {
  const element = document.querySelector(".py-20");
  if (element) {
    element.scrollIntoView({ behavior: "smooth" });
  }
};

const goToLogin = () => {
  router.push("/login");
};

onMounted(() => {
  window.addEventListener('mousemove', (e) => {
    if (cursor.value) {
      cursor.value.style.left = `${e.clientX}px`;
      cursor.value.style.top = `${e.clientY}px`;
    }
  });
});
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");

body {
  font-family: "Roboto", sans-serif;
}

.swiper-pagination-bullet {
  background-color: #4ED9F5;
}

.swiper-pagination-bullet-active {
  background-color: #3EAEC2;
}

/* 自定义输入框样式 */
input[type="text"],
input[type="email"],
input[type="password"] {
  @apply bg-white border-gray-300 text-gray-700 focus:border-[#4ED9F5] focus:ring-[#4ED9F5];
}

/* 去除number类型输入框的默认箭头 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f5f7fa;
}

::-webkit-scrollbar-thumb {
  background: #4ED9F5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3EAEC2;
}

/* 鼠标跟随效果 */
.mouse-follower {
  position: fixed;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, rgba(78, 217, 245, 0.8) 0%, rgba(78, 217, 245, 0.4) 50%, transparent 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: all 0.1s ease-out;
  box-shadow: 0 0 20px rgba(78, 217, 245, 0.6);
}

/* 波浪过渡效果 */
.wave-transition {
  position: relative;
  height: 120px;
  overflow: hidden;
  line-height: 0;
}

.wave-svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 120px;
}

.wave-fill {
  fill: #ffffff;
}

/* 动态背景形状 */
.floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.shape {
  position: absolute;
  opacity: 0.1;
  animation: float-shapes 20s infinite linear;
}

.shape-1 {
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, #4ED9F5, #3bc8e6);
  border-radius: 50%;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #3bc8e6, #2ab7d5);
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  top: 60%;
  left: 80%;
  animation-delay: 5s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #4ED9F5, #6ee0f7);
  border-radius: 50%;
  top: 30%;
  left: 70%;
  animation-delay: 10s;
}

.shape-4 {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #8ee7f9, #4ED9F5);
  border-radius: 50%;
  top: 80%;
  left: 20%;
  animation-delay: 15s;
}

.shape-5 {
  width: 70px;
  height: 70px;
  background: linear-gradient(45deg, #aeeefb, #8ee7f9);
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  top: 20%;
  left: 40%;
  animation-delay: 8s;
}

@keyframes float-shapes {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
  100% {
    transform: translateY(0px) rotate(360deg);
  }
}

/* 翻转卡片效果 */
.flip-card {
  background-color: transparent;
  width: 100%;
  height: 280px;
  perspective: 1000px;
  cursor: pointer;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flip-card-inner.flipped {
  transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.flip-card-front {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid rgba(78, 217, 245, 0.2);
}

.flip-card-back {
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%);
  transform: rotateY(180deg);
  box-shadow: 0 8px 32px rgba(78, 217, 245, 0.4);
}

.flip-card:hover .flip-card-front {
  box-shadow: 0 8px 24px rgba(78, 217, 245, 0.2);
  border-color: rgba(78, 217, 245, 0.4);
}

.custom-button {
  position: relative;
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%) !important;
  border: 1px solid transparent !important;
  border-radius: 50px !important;
  height: 56px !important;
  color: #ffffff !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  overflow: hidden !important;
  box-shadow: 0 4px 16px rgba(78, 217, 245, 0.4) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.custom-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.custom-button:hover {
  background: linear-gradient(135deg, #3bc8e6 0%, #2ab7d5 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(78, 217, 245, 0.5) !important;
}

.custom-button:hover::before {
  left: 100%;
}

.custom-button:active {
  transform: translateY(0) !important;
}
</style>

